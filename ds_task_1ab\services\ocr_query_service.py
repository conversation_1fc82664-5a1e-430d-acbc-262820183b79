import os
import tempfile
import logging
from typing import Dict, Any, Tu<PERSON>, List
from PIL import Image
import numpy as np
from .ocr_service import OCRService
from .product_recommendation_service import ProductRecommendationService

class OCRQueryService:
    """
    Service for processing OCR-based queries and providing product recommendations.
    Integrates OCR functionality with the existing product recommendation system.
    """
    
    def __init__(self):
        """Initialize the OCR query service with required components."""
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        # Initialize OCR service
        try:
            self.ocr_service = OCRService()
            if self.ocr_service.is_available():
                self.logger.info("OCR service initialized successfully")
            else:
                self.logger.warning("OCR service initialized but Tesseract is not available")
        except Exception as e:
            self.logger.error(f"Failed to initialize OCR service: {str(e)}")
            # Don't raise exception, allow service to start without OCR
            self.ocr_service = None
        
        # Initialize recommendation service
        try:
            self.recommendation_service = ProductRecommendationService()
            self.logger.info("Recommendation service initialized successfully")
        except Exception as e:
            self.logger.error(f"Failed to initialize recommendation service: {str(e)}")
            raise
    
    def process_handwritten_query(self, image_file, preprocess: bool = True) -> Dict[str, Any]:
        """
        Process a handwritten query image and return product recommendations.
        
        Args:
            image_file: Uploaded image file (Flask FileStorage object)
            preprocess (bool): Whether to apply image preprocessing for OCR
            
        Returns:
            Dict[str, Any]: Dictionary containing OCR results and product recommendations
        """
        try:
            # Check if OCR service is available
            if not self.ocr_service or not self.ocr_service.is_available():
                return {
                    'success': False,
                    'error': 'OCR service is not available. Please install Tesseract OCR.',
                    'ocr_result': {},
                    'products': [],
                    'response': 'OCR functionality is currently unavailable. Please install Tesseract OCR to use this feature.',
                    'extracted_text': ''
                }

            # Validate input
            if not image_file:
                raise ValueError("No image file provided")

            # Validate file format
            filename = image_file.filename
            if not filename or not self.ocr_service.is_supported_format(filename):
                supported_formats = ', '.join(self.ocr_service.get_supported_formats())
                raise ValueError(f"Unsupported file format. Supported formats: {supported_formats}")
            
            # Save uploaded file temporarily
            with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(filename)[1]) as temp_file:
                image_file.save(temp_file.name)
                temp_path = temp_file.name
            
            try:
                # Try OCR with different approaches to get the best result
                self.logger.info(f"Extracting text from image: {temp_path}")

                # Try without preprocessing first
                self.logger.info("Trying OCR without preprocessing...")
                ocr_result_raw = self.ocr_service.extract_text(temp_path, preprocess=False)

                # Try with preprocessing
                self.logger.info("Trying OCR with preprocessing...")
                ocr_result_processed = self.ocr_service.extract_text(temp_path, preprocess=True)

                # Choose the better result (more text or higher confidence)
                results = [
                    ('raw', ocr_result_raw),
                    ('processed', ocr_result_processed)
                ]

                # Filter out results with errors
                valid_results = [(method, result) for method, result in results if 'error' not in result]

                if not valid_results:
                    # Both methods failed
                    error_msg = f"OCR failed: {ocr_result_raw.get('error', 'Unknown error')}"
                    return {
                        'success': False,
                        'error': error_msg,
                        'ocr_result': ocr_result_raw,
                        'products': [],
                        'response': f'OCR processing failed: {error_msg}',
                        'extracted_text': ''
                    }

                # Choose the result with the most text, or highest confidence if text lengths are similar
                def score_result(method_result):
                    method, result = method_result
                    text_len = len(result.get('text', '').strip())
                    confidence = result.get('confidence', 0)
                    # Prioritize text length, but use confidence as tiebreaker
                    return text_len * 100 + confidence

                best_method, ocr_result = max(valid_results, key=score_result)
                extracted_text = ocr_result['text']

                self.logger.info(f"Best result from {best_method} method:")
                self.logger.info(f"OCR result: {ocr_result}")
                self.logger.info(f"Extracted text: '{extracted_text}'")
                self.logger.info(f"Text length: {len(extracted_text) if extracted_text else 0}")

                # Validate extracted text - be more lenient
                if not extracted_text or len(extracted_text.strip()) < 1:
                    confidence = ocr_result.get('confidence', 0)
                    return {
                        'success': False,
                        'error': f'No readable text found in the image. OCR confidence: {confidence:.1f}%',
                        'ocr_result': ocr_result,
                        'products': [],
                        'response': f'I could not read any text from the uploaded image. OCR confidence was {confidence:.1f}%. Please ensure the image contains clear, readable text and try again.',
                        'extracted_text': extracted_text or ''
                    }
                
                # Get product recommendations using extracted text
                products, response = self.recommendation_service.get_recommendations(extracted_text)
                
                # Prepare final response
                result = {
                    'success': True,
                    'extracted_text': extracted_text,
                    'ocr_result': ocr_result,
                    'products': products,
                    'response': response,
                    'query': extracted_text,
                    'total_found': len(products)
                }
                
                # Add OCR confidence information to response
                if ocr_result['confidence'] > 0:
                    confidence_msg = f" (OCR confidence: {ocr_result['confidence']:.1f}%)"
                    result['response'] += confidence_msg
                
                return result
                
            finally:
                # Clean up temporary file
                if os.path.exists(temp_path):
                    os.unlink(temp_path)
                    
        except Exception as e:
            self.logger.error(f"Error processing handwritten query: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'products': [],
                'response': f'Sorry, I encountered an error while processing your image: {str(e)}',
                'extracted_text': '',
                'ocr_result': {}
            }
    
    def process_image_from_path(self, image_path: str, preprocess: bool = True) -> Dict[str, Any]:
        """
        Process a handwritten query from an image file path.
        
        Args:
            image_path (str): Path to the image file
            preprocess (bool): Whether to apply image preprocessing for OCR
            
        Returns:
            Dict[str, Any]: Dictionary containing OCR results and product recommendations
        """
        try:
            # Validate image file
            if not self.ocr_service.validate_image(image_path):
                raise ValueError(f"Invalid or unsupported image file: {image_path}")
            
            # Extract text from image
            ocr_result = self.ocr_service.extract_text(image_path, preprocess)
            extracted_text = ocr_result['text']
            
            # Validate extracted text
            if not extracted_text or len(extracted_text.strip()) < 2:
                return {
                    'success': False,
                    'error': 'No readable text found in the image',
                    'ocr_result': ocr_result,
                    'products': [],
                    'response': 'I could not read any text from the image. Please ensure the image contains clear, readable text.',
                    'extracted_text': extracted_text
                }
            
            # Get product recommendations using extracted text
            products, response = self.recommendation_service.get_recommendations(extracted_text)
            
            # Prepare final response
            result = {
                'success': True,
                'extracted_text': extracted_text,
                'ocr_result': ocr_result,
                'products': products,
                'response': response,
                'query': extracted_text,
                'total_found': len(products)
            }
            
            # Add OCR confidence information to response
            if ocr_result['confidence'] > 0:
                confidence_msg = f" (OCR confidence: {ocr_result['confidence']:.1f}%)"
                result['response'] += confidence_msg
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error processing image from path {image_path}: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'products': [],
                'response': f'Sorry, I encountered an error while processing your image: {str(e)}',
                'extracted_text': '',
                'ocr_result': {}
            }
    
    def validate_query_image(self, image_file) -> Dict[str, Any]:
        """
        Validate an uploaded image for OCR processing.
        
        Args:
            image_file: Uploaded image file (Flask FileStorage object)
            
        Returns:
            Dict[str, Any]: Validation result
        """
        try:
            if not image_file:
                return {'valid': False, 'error': 'No image file provided'}
            
            filename = image_file.filename
            if not filename:
                return {'valid': False, 'error': 'No filename provided'}
            
            # Check file format
            if not self.ocr_service.is_supported_format(filename):
                supported_formats = ', '.join(self.ocr_service.get_supported_formats())
                return {
                    'valid': False, 
                    'error': f'Unsupported file format. Supported formats: {supported_formats}'
                }
            
            # Check file size
            image_file.seek(0, 2)  # Seek to end
            file_size = image_file.tell()
            image_file.seek(0)  # Reset to beginning
            
            if file_size < 1024:  # Less than 1KB
                return {'valid': False, 'error': 'File too small (minimum 1KB)'}
            
            if file_size > 10 * 1024 * 1024:  # More than 10MB
                return {'valid': False, 'error': 'File too large (maximum 10MB)'}
            
            return {'valid': True, 'filename': filename, 'size': file_size}
            
        except Exception as e:
            self.logger.error(f"Error validating image: {str(e)}")
            return {'valid': False, 'error': f'Validation error: {str(e)}'}
    
    def get_ocr_info(self) -> Dict[str, Any]:
        """
        Get information about the OCR service configuration.
        
        Returns:
            Dict[str, Any]: OCR service information
        """
        try:
            import pytesseract
            version = pytesseract.get_tesseract_version()
            supported_formats = self.ocr_service.get_supported_formats()
            
            return {
                'tesseract_version': str(version),
                'supported_formats': supported_formats,
                'service_status': 'active'
            }
        except Exception as e:
            self.logger.error(f"Error getting OCR info: {str(e)}")
            return {
                'tesseract_version': 'unknown',
                'supported_formats': [],
                'service_status': 'error',
                'error': str(e)
            } 