from flask import Flask, request, jsonify, render_template
import os
from dotenv import load_dotenv
from services.product_recommendation_service import ProductRecommendationService
from services.ocr_query_service import OCRQueryService

# Load environment variables
load_dotenv()

app = Flask(__name__)

# Initialize services
recommendation_service = None
ocr_query_service = None

try:
    recommendation_service = ProductRecommendationService()
    print("Product recommendation service initialized successfully")
except Exception as e:
    print(f"Warning: Could not initialize recommendation service: {e}")

try:
    ocr_query_service = OCRQueryService()
    print("OCR query service initialized successfully")
except Exception as e:
    print(f"Warning: Could not initialize OCR service: {e}")
    print("OCR functionality will be limited. Please install Tesseract OCR for full functionality.")

@app.route('/product-recommendation', methods=['POST'])
def product_recommendation():
    """
    Endpoint for product recommendations based on natural language queries.
    Input: Form data containing 'query' (string).
    Output: JSON with 'products' (array of objects) and 'response' (string).
    """
    query = request.form.get('query', '')
    
    if not query:
        return jsonify({"error": "Query is required"}), 400
    
    if not recommendation_service:
        return jsonify({"error": "Recommendation service not available"}), 500
    
    try:
        # Get recommendations - let the system return whatever number it finds
        products, response = recommendation_service.get_recommendations(query)
        
        return jsonify({
            "products": products,
            "response": response,
            "query": query,
            "total_found": len(products)
        })
        
    except Exception as e:
        return jsonify({
            "error": "An error occurred while processing your request",
            "products": [],
            "response": "Sorry, I encountered an error while searching for products. Please try again."
        }), 500

@app.route('/ocr-query', methods=['POST'])
def ocr_query():
    """
    Endpoint to process handwritten queries extracted from uploaded images.
    Input: Form data containing 'image_data' (file, base64-encoded image or direct file upload).
    Output: JSON with 'products' (array of objects) and 'response' (string).
    """
    print(f"OCR endpoint called. Files: {list(request.files.keys())}")
    print(f"Form data: {list(request.form.keys())}")

    image_file = request.files.get('image_data')

    if not image_file:
        print("No image file provided in request")
        return jsonify({"error": "No image file provided"}), 400

    print(f"Image file received: {image_file.filename}, size: {len(image_file.read())} bytes")
    image_file.seek(0)  # Reset file pointer after reading

    if not ocr_query_service:
        print("OCR service not available")
        return jsonify({
            "error": "OCR service not available. Please install Tesseract OCR.",
            "products": [],
            "response": "OCR functionality is currently unavailable. Please install Tesseract OCR to use this feature.",
            "extracted_text": ""
        }), 500

    try:
        # Process the handwritten query image
        print("Processing image with OCR service...")
        result = ocr_query_service.process_handwritten_query(image_file)
        print(f"OCR result: {result}")

        if result['success']:
            return jsonify({
                "products": result['products'],
                "response": result['response'],
                "extracted_text": result['extracted_text'],
                "ocr_confidence": result['ocr_result'].get('confidence', 0),
                "query": result['query'],
                "total_found": result['total_found']
            })
        else:
            print(f"OCR processing failed: {result['error']}")
            return jsonify({
                "error": result['error'],
                "products": result['products'],
                "response": result['response'],
                "extracted_text": result['extracted_text']
            }), 400

    except Exception as e:
        print(f"Exception in OCR endpoint: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({
            "error": f"An error occurred while processing your image: {str(e)}",
            "products": [],
            "response": "Sorry, I encountered an error while processing your image. Please try again.",
            "extracted_text": ""
        }), 500

@app.route('/image-product-search', methods=['POST'])
def image_product_search():
    """
    Endpoint to identify and suggest products from uploaded product images.
    Input: Form data containing 'product_image' (file, base64-encoded image or direct file upload).
    Output: JSON with 'products' (array of objects) and 'response' (string).
    """
    product_image = request.files.get('product_image')
    # Process the product image to detect and match products
    products = []  # Empty array, to be populated with product data
    response = ""  # Empty string, to be filled with a natural language response
    return jsonify({"products": products, "response": response})



@app.route('/sample_response', methods=['GET'])
def sample_response():
    """
    Endpoint to return a sample JSON response for the API.
    Output: JSON with 'products' (array of objects) and 'response' (string).
    """
    return render_template('sample_response.html')

@app.route('/', methods=['GET'])
def ecommerce_services_page():
    """
    Main page for e-commerce services interface.
    """
    return render_template('ecommerce_services.html')

if __name__ == '__main__':
    app.run(debug=True)
