#!/usr/bin/env python3
"""
Simple OCR Test - Test the simplified OCR implementation
"""

import sys
import os
sys.path.append('.')

def test_basic_ocr():
    """Test basic OCR functionality."""
    print("🧪 Testing Basic OCR")
    print("=" * 30)
    
    try:
        # Test pytesseract directly first
        import pytesseract
        from PIL import Image, ImageDraw
        import tempfile
        
        # Create a simple test image
        img = Image.new('RGB', (200, 60), color='white')
        draw = ImageDraw.Draw(img)
        draw.text((10, 20), "Hello World", fill='black')
        
        # Save to temp file
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.png')
        img.save(temp_file.name)
        temp_file.close()
        
        print(f"Created test image: {temp_file.name}")
        
        # Test direct pytesseract
        text = pytesseract.image_to_string(img)
        print(f"Direct OCR result: '{text.strip()}'")
        
        # Test our OCR service
        from services.ocr_service import OCRService
        ocr_service = OCRService()
        
        if ocr_service.is_available():
            result = ocr_service.extract_text(temp_file.name)
            print(f"Our OCR result: '{result.get('text', '')}'")
            print(f"Confidence: {result.get('confidence', 0):.1f}%")
            
            if result.get('text', '').strip():
                print("✅ OCR is working!")
            else:
                print("❌ OCR returned empty text")
        else:
            print("❌ OCR service not available")
        
        # Clean up
        os.unlink(temp_file.name)
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_basic_ocr()
