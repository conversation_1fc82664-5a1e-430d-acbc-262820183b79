#!/usr/bin/env python3
"""
Quick OCR Test - Simple test to verify OCR is working
"""

import sys
import os
sys.path.append('.')

def test_simple_ocr():
    """Test OCR with a simple approach like the example provided."""
    print("🧪 Quick OCR Test")
    print("=" * 40)
    
    try:
        # Test 1: Check if pytesseract works at all
        print("1. Testing pytesseract import...")
        import pytesseract
        print("✅ pytesseract imported successfully")
        
        # Test 2: Check Tesseract version
        print("\n2. Testing Tesseract version...")
        try:
            version = pytesseract.get_tesseract_version()
            print(f"✅ Tesseract version: {version}")
        except Exception as e:
            print(f"❌ Tesseract version check failed: {e}")
            return False
        
        # Test 3: Create a simple test image
        print("\n3. Creating test image...")
        from PIL import Image, ImageDraw, ImageFont
        import tempfile
        
        # Create a simple white image with black text
        img = Image.new('RGB', (300, 100), color='white')
        draw = ImageDraw.Draw(img)
        
        # Use default font
        try:
            font = ImageFont.load_default()
        except:
            font = None
        
        # Draw simple text
        test_text = "Hello World"
        draw.text((50, 30), test_text, fill='black', font=font)
        
        # Save to temporary file
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.png')
        img.save(temp_file.name)
        temp_file.close()
        
        print(f"✅ Test image created: {temp_file.name}")
        print(f"📝 Expected text: '{test_text}'")
        
        # Test 4: Simple OCR extraction (like the example)
        print("\n4. Testing simple OCR extraction...")
        
        # Load image and extract text (simple approach)
        extracted_text = pytesseract.image_to_string(img)
        print(f"📝 Raw extracted text: '{extracted_text}'")
        
        # Clean the text
        cleaned_text = extracted_text.strip()
        print(f"📝 Cleaned text: '{cleaned_text}'")
        
        # Test 5: Test with our OCR service
        print("\n5. Testing our OCR service...")
        from services.ocr_service import OCRService
        
        ocr_service = OCRService()
        if not ocr_service.is_available():
            print("❌ OCR service not available")
            return False
        
        result = ocr_service.extract_text(temp_file.name, preprocess=False)
        print(f"📊 OCR service result: {result}")
        
        # Clean up
        try:
            os.unlink(temp_file.name)
        except:
            pass
        
        # Check results
        if cleaned_text and test_text.lower() in cleaned_text.lower():
            print("\n✅ Simple OCR test PASSED!")
            return True
        elif result.get('text') and test_text.lower() in result.get('text', '').lower():
            print("\n✅ OCR service test PASSED!")
            return True
        else:
            print(f"\n❌ OCR test FAILED!")
            print(f"Expected: '{test_text}'")
            print(f"Got (simple): '{cleaned_text}'")
            print(f"Got (service): '{result.get('text', '')}'")
            return False
            
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    success = test_simple_ocr()
    
    if success:
        print("\n🎉 OCR is working! You can now test with your images.")
        print("\nTips for better OCR results:")
        print("- Use high-resolution images")
        print("- Ensure good contrast (dark text on light background)")
        print("- Avoid skewed or rotated text")
        print("- Use clear, readable fonts")
    else:
        print("\n❌ OCR test failed. Please check:")
        print("1. Tesseract installation")
        print("2. PATH configuration")
        print("3. Run: python install_tesseract.py")

if __name__ == "__main__":
    main()
