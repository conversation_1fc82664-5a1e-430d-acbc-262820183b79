#!/usr/bin/env python3
"""
Tesseract OCR Installation Helper Script
This script helps install and configure Tesseract OCR for the OCR functionality.
"""

import os
import sys
import platform
import subprocess
import urllib.request
import tempfile
from pathlib import Path

def check_tesseract_installed():
    """Check if Tesseract is already installed and accessible."""
    try:
        import pytesseract
        version = pytesseract.get_tesseract_version()
        print(f"✅ Tesseract is already installed! Version: {version}")
        return True
    except Exception as e:
        print(f"❌ Tesseract not found: {e}")
        return False

def install_windows():
    """Provide instructions for Windows installation."""
    print("\n🪟 Windows Installation Instructions:")
    print("=" * 50)
    print("1. Download Tesseract installer from:")
    print("   https://github.com/UB-Mannheim/tesseract/wiki")
    print("\n2. Choose the appropriate version:")
    print("   - For 64-bit Windows: tesseract-ocr-w64-setup-v5.x.x.exe")
    print("   - For 32-bit Windows: tesseract-ocr-w32-setup-v5.x.x.exe")
    print("\n3. Run the installer with default settings")
    print("   (Usually installs to: C:\\Program Files\\Tesseract-OCR\\)")
    print("\n4. Restart your application")
    print("\n5. If issues persist, add Tesseract to your PATH:")
    print("   - Open System Properties > Environment Variables")
    print("   - Add C:\\Program Files\\Tesseract-OCR to PATH")

def install_macos():
    """Provide instructions for macOS installation."""
    print("\n🍎 macOS Installation Instructions:")
    print("=" * 50)
    print("1. Install Homebrew if not already installed:")
    print("   /bin/bash -c \"$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\"")
    print("\n2. Install Tesseract using Homebrew:")
    print("   brew install tesseract")
    print("\n3. Restart your application")

def install_linux():
    """Provide instructions for Linux installation."""
    print("\n🐧 Linux Installation Instructions:")
    print("=" * 50)
    print("Ubuntu/Debian:")
    print("   sudo apt update")
    print("   sudo apt install tesseract-ocr")
    print("\nCentOS/RHEL/Fedora:")
    print("   sudo yum install tesseract")
    print("   # or")
    print("   sudo dnf install tesseract")
    print("\nArch Linux:")
    print("   sudo pacman -S tesseract")
    print("\n3. Restart your application")

def test_installation():
    """Test if the installation was successful."""
    print("\n🧪 Testing Tesseract installation...")
    try:
        import pytesseract
        from PIL import Image
        import numpy as np
        
        # Create a simple test image with text
        test_image = Image.new('RGB', (200, 50), color='white')
        
        # Try to extract text (should return empty or minimal text)
        result = pytesseract.image_to_string(test_image)
        
        version = pytesseract.get_tesseract_version()
        print(f"✅ Success! Tesseract {version} is working correctly.")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def main():
    """Main installation helper function."""
    print("🔍 Tesseract OCR Installation Helper")
    print("=" * 40)
    
    # Check if already installed
    if check_tesseract_installed():
        if test_installation():
            print("\n✅ Tesseract is properly configured and ready to use!")
            return
    
    # Detect operating system and provide instructions
    system = platform.system().lower()
    
    if system == "windows":
        install_windows()
    elif system == "darwin":  # macOS
        install_macos()
    elif system == "linux":
        install_linux()
    else:
        print(f"\n❓ Unsupported operating system: {system}")
        print("Please visit: https://tesseract-ocr.github.io/tessdoc/Installation.html")
    
    print("\n" + "=" * 50)
    print("After installation, restart your application and try the OCR functionality again.")
    print("If you continue to have issues, please check the Tesseract documentation.")

if __name__ == "__main__":
    main()
