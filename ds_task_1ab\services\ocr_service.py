import pytesseract
from PIL import Image
import cv2
import numpy as np
import logging
import os
from typing import Op<PERSON>, <PERSON><PERSON>, Dict, Any
import re

class OCRService:
    """
    Service for Optical Character Recognition (OCR) using Tesseract.
    Handles image preprocessing and text extraction from various image formats.
    """
    
    def __init__(self, tesseract_path: Optional[str] = None):
        """
        Initialize the OCR service.

        Args:
            tesseract_path (str, optional): Path to Tesseract executable
        """
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)

        # Try common Tesseract installation paths on Windows
        if tesseract_path is None and os.name == 'nt':  # Windows
            common_paths = [
                r"C:\Program Files\Tesseract-OCR\tesseract.exe",
                r"C:\Program Files (x86)\Tesseract-OCR\tesseract.exe",
                r"C:\Users\<USER>\AppData\Local\Programs\Tesseract-OCR\tesseract.exe".format(os.getenv('USERNAME', '')),
            ]
            for path in common_paths:
                if os.path.exists(path):
                    tesseract_path = path
                    self.logger.info(f"Found Tesseract at: {tesseract_path}")
                    break

        # Configure Tesseract path if found or provided
        if tesseract_path:
            pytesseract.pytesseract.tesseract_cmd = tesseract_path
            self.logger.info(f"Tesseract path set to: {tesseract_path}")

        # Verify Tesseract installation
        try:
            version = pytesseract.get_tesseract_version()
            self.logger.info(f"Tesseract version: {version}")
            self.tesseract_available = True
        except Exception as e:
            self.logger.error(f"Tesseract not found or not properly configured: {str(e)}")
            self.tesseract_available = False
            self._log_installation_instructions()

    def _log_installation_instructions(self):
        """Log installation instructions for Tesseract."""
        self.logger.error("=" * 60)
        self.logger.error("TESSERACT OCR NOT FOUND")
        self.logger.error("=" * 60)
        self.logger.error("Please install Tesseract OCR:")
        self.logger.error("")
        self.logger.error("Windows:")
        self.logger.error("1. Download from: https://github.com/UB-Mannheim/tesseract/wiki")
        self.logger.error("2. Install to default location")
        self.logger.error("3. Restart your application")
        self.logger.error("")
        self.logger.error("macOS:")
        self.logger.error("brew install tesseract")
        self.logger.error("")
        self.logger.error("Ubuntu/Debian:")
        self.logger.error("sudo apt update && sudo apt install tesseract-ocr")
        self.logger.error("=" * 60)

    def is_available(self) -> bool:
        """Check if Tesseract is available for use."""
        return self.tesseract_available

    def preprocess_image(self, image: np.ndarray) -> np.ndarray:
        """
        Preprocess image to improve OCR accuracy.

        Args:
            image (np.ndarray): Input image as numpy array

        Returns:
            np.ndarray: Preprocessed image
        """
        try:
            # Convert to grayscale if not already
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image.copy()

            # Simple preprocessing - just resize if image is too small
            height, width = gray.shape
            if height < 100 or width < 100:
                # Scale up small images
                scale_factor = max(2, 100 / min(height, width))
                new_width = int(width * scale_factor)
                new_height = int(height * scale_factor)
                gray = cv2.resize(gray, (new_width, new_height), interpolation=cv2.INTER_CUBIC)
                self.logger.info(f"Resized image from {width}x{height} to {new_width}x{new_height}")

            # Optional: Apply gentle noise reduction only if needed
            # denoised = cv2.medianBlur(gray, 3)

            # Optional: Apply thresholding only if the image is very unclear
            # _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

            return gray  # Return the grayscale image without aggressive processing

        except Exception as e:
            self.logger.error(f"Error preprocessing image: {str(e)}")
            # Return original image if preprocessing fails
            return image
    
    def extract_text(self, image_path: str, preprocess: bool = True) -> Dict[str, Any]:
        """
        Extract text from an image file.

        Args:
            image_path (str): Path to the image file
            preprocess (bool): Whether to apply image preprocessing

        Returns:
            Dict[str, Any]: Dictionary containing extracted text and metadata
        """
        if not self.tesseract_available:
            return {
                'text': '',
                'raw_text': '',
                'confidence': 0,
                'word_count': 0,
                'preprocessed': preprocess,
                'error': 'Tesseract OCR is not available. Please install Tesseract OCR.'
            }

        try:
            # Validate file exists
            if not os.path.exists(image_path):
                raise FileNotFoundError(f"Image file not found: {image_path}")

            # Load image
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError(f"Could not load image: {image_path}")

            return self.extract_text_from_array(image, preprocess)

        except Exception as e:
            self.logger.error(f"Error extracting text from image {image_path}: {str(e)}")
            raise
    
    def extract_text_from_array(self, image: np.ndarray, preprocess: bool = True) -> Dict[str, Any]:
        """
        Extract text from a numpy array image.

        Args:
            image (np.ndarray): Input image as numpy array
            preprocess (bool): Whether to apply image preprocessing

        Returns:
            Dict[str, Any]: Dictionary containing extracted text and metadata
        """
        if not self.tesseract_available:
            return {
                'text': '',
                'raw_text': '',
                'confidence': 0,
                'word_count': 0,
                'preprocessed': preprocess,
                'error': 'Tesseract OCR is not available. Please install Tesseract OCR.'
            }

        try:
            self.logger.info(f"Processing image with preprocessing: {preprocess}")

            # Simple approach like in the example - direct OCR extraction
            if preprocess:
                # Apply preprocessing
                processed_image = self.preprocess_image(image)
                self.logger.info("Using preprocessed image for OCR")
                ocr_image = processed_image
            else:
                # Use original image
                self.logger.info("Using original image for OCR")
                ocr_image = image

            # Extract text using Tesseract with configuration for better accuracy
            self.logger.info("Extracting text with pytesseract...")

            # Use custom configuration for better text recognition
            # --oem 3: Use default OCR Engine Mode (LSTM + Legacy)
            # --psm 6: Assume a single uniform block of text
            custom_config = r'--oem 3 --psm 6'

            try:
                raw_text = pytesseract.image_to_string(ocr_image, config=custom_config)
                self.logger.info(f"OCR with config '{custom_config}': '{raw_text}'")
            except Exception as e:
                self.logger.warning(f"OCR with custom config failed: {e}, trying default...")
                # Fallback to default configuration
                raw_text = pytesseract.image_to_string(ocr_image)
                self.logger.info(f"OCR with default config: '{raw_text}'")

            # Clean the extracted text
            cleaned_text = self.clean_extracted_text(raw_text)
            self.logger.info(f"Cleaned text: '{cleaned_text}'")

            # Calculate confidence more safely
            try:
                self.logger.info("Calculating confidence scores...")
                # Use the same configuration for confidence calculation
                confidence_data = pytesseract.image_to_data(ocr_image, config=custom_config, output_type=pytesseract.Output.DICT)

                # Extract confidence values and filter out -1 (no confidence) and 0 (no text)
                confidences = []
                words = []

                for i, conf in enumerate(confidence_data['conf']):
                    try:
                        conf_val = float(conf)
                        if conf_val > 0:  # Only include positive confidence values
                            confidences.append(conf_val)
                            # Also get the corresponding word for debugging
                            if i < len(confidence_data.get('text', [])):
                                word = confidence_data['text'][i].strip()
                                if word:  # Only include non-empty words
                                    words.append(f"{word}({conf_val:.0f}%)")
                    except (ValueError, TypeError):
                        continue

                if confidences:
                    avg_confidence = sum(confidences) / len(confidences)
                    self.logger.info(f"Found {len(confidences)} confident words: {words[:5]}...")
                    self.logger.info(f"Average confidence: {avg_confidence:.1f}%")
                else:
                    avg_confidence = 0
                    self.logger.warning("No valid confidence values found")

            except Exception as e:
                self.logger.warning(f"Confidence calculation failed: {e}")
                # Try without custom config
                try:
                    confidence_data = pytesseract.image_to_data(ocr_image, output_type=pytesseract.Output.DICT)
                    confidences = [float(conf) for conf in confidence_data['conf'] if float(conf) > 0]
                    avg_confidence = sum(confidences) / len(confidences) if confidences else 0
                except:
                    avg_confidence = 0

            # Calculate word count
            word_count = len(cleaned_text.split()) if cleaned_text else 0

            result = {
                'text': cleaned_text,
                'raw_text': raw_text,
                'confidence': float(avg_confidence) if not np.isnan(avg_confidence) else 0.0,
                'word_count': word_count,
                'preprocessed': preprocess
            }

            self.logger.info(f"Final OCR result: text_length={len(cleaned_text)}, confidence={result['confidence']:.1f}%, words={word_count}")
            return result

        except Exception as e:
            self.logger.error(f"Error extracting text from image array: {str(e)}")
            import traceback
            traceback.print_exc()
            return {
                'text': '',
                'raw_text': '',
                'confidence': 0.0,
                'word_count': 0,
                'preprocessed': preprocess,
                'error': f'OCR processing failed: {str(e)}'
            }
    
    def extract_text_from_pil(self, image: Image.Image, preprocess: bool = True) -> Dict[str, Any]:
        """
        Extract text from a PIL Image object.
        
        Args:
            image (Image.Image): Input PIL image
            preprocess (bool): Whether to apply image preprocessing
            
        Returns:
            Dict[str, Any]: Dictionary containing extracted text and metadata
        """
        try:
            # Convert PIL image to numpy array
            image_array = np.array(image)
            
            # Convert RGB to BGR if needed (OpenCV uses BGR)
            if len(image_array.shape) == 3 and image_array.shape[2] == 3:
                image_array = cv2.cvtColor(image_array, cv2.COLOR_RGB2BGR)
            
            return self.extract_text_from_array(image_array, preprocess)
            
        except Exception as e:
            self.logger.error(f"Error extracting text from PIL image: {str(e)}")
            raise
    
    def clean_extracted_text(self, text: str) -> str:
        """
        Clean and normalize extracted text.

        Args:
            text (str): Raw extracted text

        Returns:
            str: Cleaned text
        """
        if not text:
            return ""

        try:
            # Normalize line breaks first
            cleaned = text.replace('\n', ' ').replace('\r', ' ')

            # Remove extra whitespace but be less aggressive
            cleaned = re.sub(r'\s+', ' ', cleaned.strip())

            # Only remove obvious OCR artifacts, keep most punctuation
            # Remove only non-printable characters and some common OCR errors
            cleaned = re.sub(r'[^\w\s\-.,!?@#$%&*()+=\[\]{}|;:\'\"<>/\\]', '', cleaned)

            # Final whitespace cleanup
            cleaned = re.sub(r'\s+', ' ', cleaned).strip()

            self.logger.debug(f"Text cleaning: '{text}' -> '{cleaned}'")
            return cleaned

        except Exception as e:
            self.logger.error(f"Error cleaning text: {str(e)}")
            return text.strip() if text else ""
    
    def validate_image(self, image_path: str) -> bool:
        """
        Validate if the image file is suitable for OCR.
        
        Args:
            image_path (str): Path to the image file
            
        Returns:
            bool: True if image is valid for OCR
        """
        try:
            # Check file exists
            if not os.path.exists(image_path):
                return False
            
            # Check file size (not too small, not too large)
            file_size = os.path.getsize(image_path)
            if file_size < 1024 or file_size > 10 * 1024 * 1024:  # 1KB to 10MB
                return False
            
            # Try to load image
            image = cv2.imread(image_path)
            if image is None:
                return False
            
            # Check image dimensions
            height, width = image.shape[:2]
            if height < 50 or width < 50:  # Minimum size
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error validating image {image_path}: {str(e)}")
            return False
    
    def get_supported_formats(self) -> list:
        """
        Get list of supported image formats.
        
        Returns:
            list: List of supported file extensions
        """
        return ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif', '.gif']
    
    def is_supported_format(self, file_path: str) -> bool:
        """
        Check if the file format is supported for OCR.
        
        Args:
            file_path (str): Path to the file
            
        Returns:
            bool: True if format is supported
        """
        _, ext = os.path.splitext(file_path.lower())
        return ext in self.get_supported_formats() 