#!/usr/bin/env python3
"""
OCR Test Script
This script helps test the OCR functionality independently.
"""

import sys
import os
sys.path.append('.')

def test_tesseract_installation():
    """Test if Tesseract is properly installed."""
    print("🔍 Testing Tesseract Installation...")
    print("=" * 50)
    
    try:
        import pytesseract
        version = pytesseract.get_tesseract_version()
        print(f"✅ Tesseract version: {version}")
        return True
    except Exception as e:
        print(f"❌ Tesseract not found: {e}")
        return False

def test_ocr_service():
    """Test the OCR service."""
    print("\n🔧 Testing OCR Service...")
    print("=" * 50)
    
    try:
        from services.ocr_service import OCRService
        ocr_service = OCRService()
        
        if ocr_service.is_available():
            print("✅ OCR Service initialized successfully")
            return ocr_service
        else:
            print("❌ OCR Service not available")
            return None
    except Exception as e:
        print(f"❌ OCR Service failed: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_ocr_query_service():
    """Test the OCR query service."""
    print("\n🔧 Testing OCR Query Service...")
    print("=" * 50)
    
    try:
        from services.ocr_query_service import OCRQueryService
        ocr_query_service = OCRQueryService()
        print("✅ OCR Query Service initialized successfully")
        return ocr_query_service
    except Exception as e:
        print(f"❌ OCR Query Service failed: {e}")
        import traceback
        traceback.print_exc()
        return None

def create_test_image():
    """Create a simple test image with text."""
    print("\n🖼️ Creating test image...")
    print("=" * 50)
    
    try:
        from PIL import Image, ImageDraw, ImageFont
        import tempfile
        
        # Create a simple image with text
        img = Image.new('RGB', (400, 100), color='white')
        draw = ImageDraw.Draw(img)
        
        # Try to use a default font, fallback to basic if not available
        try:
            font = ImageFont.truetype("arial.ttf", 24)
        except:
            font = ImageFont.load_default()
        
        text = "Hello World Test"
        draw.text((50, 30), text, fill='black', font=font)
        
        # Save to temporary file
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.png')
        img.save(temp_file.name)
        temp_file.close()
        
        print(f"✅ Test image created: {temp_file.name}")
        print(f"📝 Text in image: '{text}'")
        return temp_file.name, text
        
    except Exception as e:
        print(f"❌ Failed to create test image: {e}")
        return None, None

def test_ocr_on_image(ocr_service, image_path, expected_text):
    """Test OCR on a specific image."""
    print(f"\n🔍 Testing OCR on image: {image_path}")
    print("=" * 50)
    
    try:
        result = ocr_service.extract_text(image_path, preprocess=True)
        print(f"📊 OCR Result: {result}")
        
        extracted_text = result.get('text', '')
        confidence = result.get('confidence', 0)
        
        print(f"📝 Expected: '{expected_text}'")
        print(f"📝 Extracted: '{extracted_text}'")
        print(f"📊 Confidence: {confidence:.1f}%")
        
        if extracted_text and expected_text.lower() in extracted_text.lower():
            print("✅ OCR test PASSED")
            return True
        else:
            print("❌ OCR test FAILED")
            return False
            
    except Exception as e:
        print(f"❌ OCR test error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    print("🧪 OCR Functionality Test Suite")
    print("=" * 60)
    
    # Test 1: Tesseract installation
    if not test_tesseract_installation():
        print("\n❌ Tesseract is not installed. Please install it first.")
        print("Run: python install_tesseract.py")
        return
    
    # Test 2: OCR Service
    ocr_service = test_ocr_service()
    if not ocr_service:
        print("\n❌ OCR Service failed to initialize.")
        return
    
    # Test 3: OCR Query Service
    ocr_query_service = test_ocr_query_service()
    if not ocr_query_service:
        print("\n❌ OCR Query Service failed to initialize.")
        return
    
    # Test 4: Create and test with a simple image
    image_path, expected_text = create_test_image()
    if image_path:
        success = test_ocr_on_image(ocr_service, image_path, expected_text)
        
        # Clean up
        try:
            os.unlink(image_path)
        except:
            pass
        
        if success:
            print("\n✅ All tests PASSED! OCR functionality is working.")
        else:
            print("\n❌ OCR test FAILED. Check Tesseract configuration.")
    else:
        print("\n❌ Could not create test image.")
    
    print("\n" + "=" * 60)
    print("Test complete. If tests failed, check:")
    print("1. Tesseract installation")
    print("2. PATH configuration")
    print("3. Image quality and text clarity")

if __name__ == "__main__":
    main()
